'use client';

import React, { useState } from 'react';
import { useAICompletions } from '@/hooks/useAICompletions';
import { CompletionRequest } from '@/services/aiCompletionsService';

export default function AICompletionsDemo() {
  const { loading, error, response, generateCompletion, generateText, reset } = useAICompletions();
  const [prompt, setPrompt] = useState('วันนี้วันที่เท่าไร');
  const [model, setModel] = useState('openai/gpt-4o');
  const [maxTokens, setMaxTokens] = useState(100);
  const [temperature, setTemperature] = useState(0.7);

  const handleGenerateCompletion = async () => {
    const request: CompletionRequest = {
      model,
      prompt,
      max_tokens: maxTokens,
      temperature,
    };

    await generateCompletion(request);
  };

  const handleQuickTest = async () => {
    const result = await generateText('สวัสดีครับ ช่วยแนะนำตัวหน่อย', {
      model: 'openai/gpt-4.1',
      max_tokens: 100,
      temperature: 0.7,
    });
    console.log('Quick test result:', result);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-6">AI Completions Demo</h1>
        
        {/* Input Form */}
        <div className="space-y-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Model
            </label>
            <select
              value={model}
              onChange={(e) => setModel(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="openai/gpt-4.1">GPT-4.1</option>
              <option value="openai/gpt-3.5-turbo">GPT-3.5 Turbo</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Prompt
            </label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your prompt here..."
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Tokens
              </label>
              <input
                type="number"
                value={maxTokens}
                onChange={(e) => setMaxTokens(Number(e.target.value))}
                min="1"
                max="2000"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Temperature
              </label>
              <input
                type="number"
                value={temperature}
                onChange={(e) => setTemperature(Number(e.target.value))}
                min="0"
                max="2"
                step="0.1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-4 mb-6">
          <button
            onClick={handleGenerateCompletion}
            disabled={loading || !prompt.trim()}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {loading && (
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            Generate Completion
          </button>

          <button
            onClick={handleQuickTest}
            disabled={loading}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Quick Test
          </button>

          <button
            onClick={reset}
            className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            Reset
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <h3 className="text-lg font-medium text-red-800 mb-2">Error</h3>
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {/* Response Display */}
        {response && (
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-md">
              <h3 className="text-lg font-medium text-green-800 mb-2">Generated Text</h3>
              <p className="text-green-700 whitespace-pre-wrap">
                {response.choices[0]?.text}
              </p>
            </div>

            <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
              <h3 className="text-lg font-medium text-gray-800 mb-2">Response Details</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">ID:</span> {response.id}
                </div>
                <div>
                  <span className="font-medium">Model:</span> {response.model}
                </div>
                <div>
                  <span className="font-medium">Created:</span> {new Date(response.created * 1000).toLocaleString()}
                </div>
                <div>
                  <span className="font-medium">Finish Reason:</span> {response.choices[0]?.finish_reason}
                </div>
              </div>
              
              {response.usage && (
                <div className="mt-4">
                  <h4 className="font-medium text-gray-700 mb-2">Token Usage</h4>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Prompt:</span> {response.usage.prompt_tokens}
                    </div>
                    <div>
                      <span className="font-medium">Completion:</span> {response.usage.completion_tokens}
                    </div>
                    <div>
                      <span className="font-medium">Total:</span> {response.usage.total_tokens}
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h3 className="text-lg font-medium text-blue-800 mb-2">Full Response JSON</h3>
              <pre className="text-xs text-blue-700 overflow-x-auto">
                {JSON.stringify(response, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
