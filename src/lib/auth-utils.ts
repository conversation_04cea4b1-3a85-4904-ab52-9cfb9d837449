import { NextRequest } from 'next/server';
import { verifyJwt } from '@/lib/jwt';
import { prisma } from '@/lib/prisma';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
export async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

// Type for successful authentication result
export interface AuthResult {
  userId: number;
  isOwner: boolean;
  isAdmin: boolean;
  isMember: boolean;
}

// Type for authentication error result
export interface AuthError {
  error: string;
  status: number;
}

// Type guard to check if authentication was successful
export function isAuthSuccess(auth: AuthResult | AuthError): auth is AuthResult {
  return 'userId' in auth;
}
