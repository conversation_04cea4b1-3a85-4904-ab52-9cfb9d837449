// AI Completions Service for LiteLLM API
export interface CompletionRequest {
  model: string;
  prompt: string;
  max_tokens: number;
  temperature: number;
}

export interface CompletionChoice {
  finish_reason: string;
  index: number;
  text: string;
  logprobs: null;
}

export interface CompletionUsage {
  completion_tokens: number;
  prompt_tokens: number;
  total_tokens: number;
  completion_tokens_details: {
    accepted_prediction_tokens: number;
    audio_tokens: number;
    reasoning_tokens: number;
    rejected_prediction_tokens: number;
  };
  prompt_tokens_details: {
    audio_tokens: number;
    cached_tokens: number;
  };
}

export interface CompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: CompletionChoice[];
  usage: CompletionUsage;
}

export class AICompletionsService {
  private readonly endpoint: string;
  private readonly authorization: string;

  constructor() {
    // In browser environment, only NEXT_PUBLIC_ variables are available
    this.endpoint = process.env.NEXT_PUBLIC_LITE_LLM_ENDPOINT || '';
    this.authorization = process.env.NEXT_PUBLIC_LITE_LLM_API_KEY || '';
    
    if (!this.endpoint) {
      throw new Error('NEXT_PUBLIC_LITE_LLM_ENDPOINT environment variable is required');
    }
    
    if (!this.authorization) {
      throw new Error('NEXT_PUBLIC_LITE_LLM_API_KEY environment variable is required');
    }
  }

  async createCompletion(request: CompletionRequest): Promise<CompletionResponse> {
    try {
      const response = await fetch(`${this.endpoint}/v1/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authorization}`,
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`AI Completions API error: ${response.status} - ${errorText}`);
      }

      const data: CompletionResponse = await response.json();
      return data;
    } catch (error) {
      console.error('Error calling AI Completions API:', error);
      throw error;
    }
  }

  // Convenience method with default parameters
  async generateText(
    prompt: string,
    options: Partial<Omit<CompletionRequest, 'prompt'>> = {}
  ): Promise<string> {
    const request: CompletionRequest = {
      model: options.model || 'openai/gpt-4.1',
      prompt,
      max_tokens: options.max_tokens || 50,
      temperature: options.temperature || 0.7,
    };

    const response = await this.createCompletion(request);
    return response.choices[0]?.text || '';
  }

  // Example usage method
  async askCurrentDate(): Promise<string> {
    return this.generateText('วันนี้วันที่เท่าไร', {
      model: 'openai/gpt-4.1',
      max_tokens: 50,
      temperature: 0.7,
    });
  }
}

// Export singleton instance
export const aiCompletionsService = new AICompletionsService();

// Export default for easier imports
export default aiCompletionsService;
