import { useState, useCallback } from 'react';
import { CompletionRequest, CompletionResponse, aiCompletionsService } from '@/services/aiCompletionsService';

interface UseAICompletionsState {
  loading: boolean;
  error: string | null;
  response: CompletionResponse | null;
}

interface UseAICompletionsReturn extends UseAICompletionsState {
  generateCompletion: (request: CompletionRequest) => Promise<CompletionResponse | null>;
  generateText: (prompt: string, options?: Partial<Omit<CompletionRequest, 'prompt'>>) => Promise<string>;
  reset: () => void;
}

export function useAICompletions(): UseAICompletionsReturn {
  const [state, setState] = useState<UseAICompletionsState>({
    loading: false,
    error: null,
    response: null,
  });

  const reset = useCallback(() => {
    setState({
      loading: false,
      error: null,
      response: null,
    });
  }, []);

  const generateCompletion = useCallback(async (request: CompletionRequest): Promise<CompletionResponse | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const data = await aiCompletionsService.createCompletion(request);
      
      setState({
        loading: false,
        error: null,
        response: data,
      });

      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setState({
        loading: false,
        error: errorMessage,
        response: null,
      });
      return null;
    }
  }, []);

  const generateText = useCallback(async (
    prompt: string,
    options: Partial<Omit<CompletionRequest, 'prompt'>> = {}
  ): Promise<string> => {
    const request: CompletionRequest = {
      model: options.model || 'openai/gpt-4.1',
      prompt,
      max_tokens: options.max_tokens || 50,
      temperature: options.temperature || 0.7,
    };

    const response = await generateCompletion(request);
    return response?.choices[0]?.text || '';
  }, [generateCompletion]);

  return {
    ...state,
    generateCompletion,
    generateText,
    reset,
  };
}
