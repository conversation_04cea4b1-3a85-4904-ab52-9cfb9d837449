# Chat Notification Implementation

## Overview

This document describes the implementation of the chat notification logic for managing chat rooms and tabs based on socket events. The implementation handles real-time chat notifications and updates the UI accordingly.

## Key Features

1. **Smart Tab Management**: Only fetches data when user is not on the corresponding tab
2. **Real-time Chat Updates**: Updates existing chats and moves them to the top of the list
3. **New Chat Creation**: Creates new chat entries when they don't exist in the current list
4. **Unread Count Management**: <PERSON>perly manages unread counts at both chat and tab levels

## Implementation Details

### Socket Event Handler

The main logic is implemented in the `chat_notification` socket event handler in `/src/app/(main)/chat/page.tsx`:

```typescript
useSocketEvent('chat_notification', (messageData: any) => {
  // Validation and data extraction
  const message = messageData.data.message;
  const user = messageData.data.user;
  const chatId = message.chatId?.toString();
  const chatType = message.chatType?.toLowerCase() as RoomType;
  
  // Skip own messages
  if (message.userId?.toString() === currentUser.id?.toString()) {
    return;
  }

  // Check if user is on the corresponding tab
  const isOnCurrentTab = activeTab === chatType;
  
  if (!isOnCurrentTab) {
    // Fetch fresh data for unread count update
    loadChatsByType(chatType);
  } else {
    // Update existing chat or create new one
    // ... (detailed logic below)
  }
});
```

### Logic Flow

#### 1. When User is NOT on the Corresponding Tab

```typescript
if (!isOnCurrentTab) {
  console.log(`User not on ${chatType} tab, refreshing tab data for unread count update`);
  loadChatsByType(chatType);
}
```

- Calls `loadChatsByType(chatType)` to fetch fresh chat data
- This ensures the tab badge shows the correct unread count
- Avoids unnecessary real-time updates when user isn't viewing that tab

#### 2. When User IS on the Corresponding Tab

The handler checks if the chat exists in the current list:

##### If Chat Exists:
```typescript
if (existingChatIndex >= 0) {
  // Update chat with new message data
  const updatedChat: Room = {
    ...existingChat,
    lastMessage: message.content,
    lastMessageTime: new Date().toISOString(),
    lastMessageType: message.messageType || 'TEXT',
    lastMessageStatus: message.messageStatus || 'DELIVERED',
    unreadCount: (existingChat.unreadCount || 0) + 1
  };
  
  // Move to top of list
  roomsOfType.splice(existingChatIndex, 1);
  roomsOfType.unshift(updatedChat);
}
```

##### If Chat Does NOT Exist:
```typescript
else {
  console.log(`Creating new chat ${chatId} at top of list`);
  createChatFromNotification(messageData, chatType);
}
```

### Helper Functions

#### `createChatFromNotification`

Creates a new chat room from notification data:

```typescript
const createChatFromNotification = (messageData: any, chatType: RoomType) => {
  const chatId = messageData.data.message.chatId.toString();
  const user = messageData.data.user;
  const message = messageData.data.message;
  
  const newRoom: Room = {
    id: chatId,
    name: getDefaultChatName({
      chatType: chatType.toUpperCase(),
      chatUsers: [{ user }]
    }, true),
    type: chatType,
    lastMessage: message.content,
    lastMessageTime: new Date().toISOString(),
    lastMessageType: message.messageType || 'TEXT',
    lastMessageStatus: message.messageStatus || 'DELIVERED',
    unreadCount: 1,
    isOnline: chatType === 'private' ? true : undefined,
    chatUsers: [{ user }]
  };

  setRooms(prev => ({
    ...prev,
    [chatType]: [newRoom, ...prev[chatType]]
  }));
};
```

## Data Structure

### Expected Socket Event Data Structure

```typescript
interface ChatNotificationData {
  message: string;
  data: {
    user: {
      id: number;
      firstName: string;
      lastName: string;
      imageUrl?: string;
    };
    message: {
      chatId: number;
      chatType: 'private' | 'task' | 'department' | 'organization';
      userId: number;
      content: string;
      messageType?: 'TEXT' | 'IMAGE' | 'FILE' | 'STICKER';
      messageStatus?: 'SENDING' | 'DELIVERED' | 'READ' | 'FAILED';
    };
  };
}
```

### Room Interface

```typescript
interface Room {
  id: string;
  name: string;
  type: RoomType;
  lastMessage?: string;
  lastMessageTime?: string;
  lastMessageType?: string;
  lastMessageStatus?: string;
  unreadCount?: number;
  isOnline?: boolean;
  avatar?: string;
  chatUsers?: Array<{
    user: {
      id: number;
      firstName: string;
      lastName: string;
    };
  }>;
}
```

## Benefits

1. **Performance Optimization**: Only fetches data when necessary
2. **Real-time Updates**: Immediate UI updates for active tabs
3. **Accurate Unread Counts**: Proper management of unread message counts
4. **User Experience**: Chats with new activity move to the top for easy access
5. **Scalability**: Efficient handling of multiple chat types and rooms

## Testing

The implementation can be tested by:

1. Opening multiple browser tabs with different users
2. Sending messages between users in different chat types
3. Verifying that unread counts update correctly
4. Checking that chats move to the top when receiving new messages
5. Ensuring tab badges show accurate unread counts

## Migration Notes

- Moved socket event handler from `ChatSidebar.tsx` to `page.tsx` for better state management
- Removed unused socket import from `ChatSidebar.tsx`
- Added helper functions for chat creation and management
- Enhanced error handling and validation for socket events
